FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - API para ...

## Users [/api/v1/users]

### Request support actions [GET /api/v1/users/support{?param1}{&param2}{&param3}]

+ Clientes: Mobile app

+ Propósito: Solicitar distintas acciones como nueva tarjeta, bloqueo o liberación

+ Parameters
    + param1: block_card (string, required) - acción a realizar. Valores válidos: block_card, new_card, release_card
    + param2: 0xaa0a0aaa (string, required) - identificador de una tarjeta
    + param3: <EMAIL> (string, required) - email del usuario de la app

+ Response 200 (application/json)

    + Attributes(object)
        + emailSent: OK (string)


+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 4 (number) - identificador del error
        + result_message: The following parameters are required: key_msg, uid, emailAddress
