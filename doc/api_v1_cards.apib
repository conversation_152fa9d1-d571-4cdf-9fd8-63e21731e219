FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - API para manejo de facturas

## Card [/api/v1/cards]

### Search bills by card [GET /api/v1/cards/{cardId}/bills{?limit}]

+ Clientes: BackOffice

+ Propósito: Para listar las últimas facturas asociadas a una tarjeta

+ Parameters
    + cardId: 1 (number, required) - identificador de una tarjeta
    + limit: 1 (number)

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + bills: (array) - lista de facturas
            + bill: (object)
                + timestamp: `2017-07-24 18:15:34.0` (string) - fecha de creación de la factura
                + serie: A (string) - letra identificadora de serie
                + number: 1 (number) - número de serie
                + total: 1 (number) - monto de una factura

+ Response 409 (application/json)

    + Attributes(object)
        + result_code: 70 (number) - identificador del error
        + result_message: La tarjeta ingresada no es de tipo prepago (string) - código interno de resultado de la operación


### Create card [POST /api/v1/cards]

+ Clientes: BackOffice

+ Propósito: Crear una tarjeta

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + uuid: 0x12345678 (string, required) - valor uuid de una tarjeta
        + master: false (boolean) - indica si una tarjeta es master o no
        + contract_type: POSTPAID (string, required) - tipo de contrato. Valores esperados: 'POSTPAID', 'PREPAID', 'MIXED'
        + start_time: `2023-02-27 21:00` (string) - inicio hora de uso
        + end_time: `2023-02-27` (string) - final de hora de uso
        + discount: `0.0` (number) - duescuento en uso asignado a una tarjeta
        + periodEnd: `2023-01-31` (string) - fecha de fin de período


+ Response 201 (application/json)

+ Response 400 (application/json)

    + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_message: Parámetros inválidos (string) - código interno de resultado de la operación
