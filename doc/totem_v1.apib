FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - TOTEM API

- Clientes: TOTEM

### Solicitar reserva de una maquina [POST /totem/v1/booking]

- Propósito: Desde el Totem se solicita una reserva de una máquina previo al inicio de una transacción.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

             + serial: 712KWFN6T723 (string) - identificador único de las máquinas proveniente de fábrica

+ Response 201 (application/json)

    + Attributes(object)
        + id: x1x11111-1x11-11x1-xx (string) - id de la reserva
        + serial: 712KWFN6T723 (string) - identificador único de las máquinas proveniente de fábrica

### Cancela la reserva de una maquina [DELETE /totem/v1/booking/{booking_id}]

- Propósito: Desde el Totem se cancela una reserva de una maquina, para los casos donde el usuario cancela la operación.


+ Parameters
    + booking_id: x1x11111-1x11-11x1-xx (string) - id de la reserva


+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111


+ Response 204 (application/json)


### Consultar estado de la reserva de una maquina [GET /totem/v1/booking/{booking_id}]

- Propósito: Desde el Totem se consulta por el estado de una reserva de una máquina, para saber si la misma está disponible.


+ Parameters
    + booking_id: x1x11111-1x11-11x1-xx (string) - id de la reserva


+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + id: x1x11111-1x11-11x1-xx (string) - id de la reserva
        + serial: 712KWFN6T723 (string) - identificador único de las máquinas proveniente de fábrica
        + status: pending (string) - estado de la reserva. otros valores posibles: unavailable, confirmed.
