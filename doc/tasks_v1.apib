FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - Tasks API

- Clientes: EC2 crontab

### Recargar usos usuarios de Coliving

- Propósito: Tarea diaria para identificar y recargar los usos que hicieron los usuarios Coliving en el período.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)


### Invalidar tokens de confirmación de cuenta para usuarios Coliving

- Propósito: Tokens de confirmación de cuenta tiene una vida útil de 15 días. Es necesario invalidar cuando este tiempo ocurra.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)
