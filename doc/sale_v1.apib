FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - Sale notification <> API
## Sale notification
### Notificar venta a Shopping (origen Bill) [POST /sale/v1/notify/{buildingId}/bill/{billId}]

- Clientes: BackOffice

- Propósito: Notificación de venta manual con origen Bill.

+ Parameters
    + buildingId: 32 (number) - Id de edificio.
    + billId: 35831 (number) - Id de la factura.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 168 (number) - identificador del error
            + result_message: SALE_ALREADY_NOTIFIED (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 166 (number) - identificador del error
            + result_message: SOAP_SERVICE_ERROR (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 121 (number) - identificador del error
            + result_message: BILL_NOT_FOUND (string) - código interno de resultado de la operación

+ Response 501 (application/json)

     + Attributes(object)
            + result_code: 13 (number) - identificador del error
            + result_message: NOT_IMPLEMENTED (string) - código interno de resultado de la operación
        
### Notificar venta a Shopping origen MachineUse [POST /sale/v1/notify/{buildingId}/bill/{billId}]

- Clientes: BackOffice

- Propósito: Notificación de venta manual con origen Machine Use.

+ Parameters
    + buildingId: 32 (number) - Id de edificio.
    + billId: 35831 (number) - Id del uso.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 400 (application/json)
     + Attributes(object)
            + result_code: 169 (number) - identificador del error
            + result_message: CARD_IS_MASTER (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 168 (number) - identificador del error
            + result_message: SALE_ALREADY_NOTIFIED (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 166 (number) - identificador del error
            + result_message: SOAP_SERVICE_ERROR (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 68 (number) - identificador del error
            + result_message: MACHINE_USE_NOT_FOUND (string) - código interno de resultado de la operación

+ Response 501 (application/json)

     + Attributes(object)
            + result_code: 13 (number) - identificador del error
            + result_message: NOT_IMPLEMENTED (string) - código interno de resultado de la operación

### (Bulk) Notificar ventas a Shopping origenes Bill y MachineUse [POST /sale/v1/notify/{buildingId}/bulk]

- Clientes: Cronjob

- Propósito: Notificación de ventas automático con origen Bill y Machine Use.

+ Parameters
    + buildingId: 32 (number) - Id de edificio.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 166 (number) - identificador del error
            + result_message: SOAP_SERVICE_ERROR (string) - código interno de resultado de la operación

+ Response 501 (application/json)

     + Attributes(object)
            + result_code: 13 (number) - identificador del error
            + result_message: NOT_IMPLEMENTED (string) - código interno de resultado de la operación

## Sale notification
### Notificar devolución de venta a Shopping (origen Bill) [POST /sale/v1/refund/{buildingId}/bill/{billId}]

- Clientes: BackOffice

- Propósito: Notificación de devolución de venta manual con origen Bill.

+ Parameters
    + buildingId: 32 (number) - Id de edificio.
    + billId: 35831 (number) - Id de la factura.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 400 (application/json)

+ Attributes(object)
            + result_code: 167 (number) - identificador del error
            + result_message: SALE_ALREADY_REFUNDED (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 166 (number) - identificador del error
            + result_message: SOAP_SERVICE_ERROR (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 170 (number) - identificador del error
            + result_message: SALE_NOT_NOTIFIED (string) - código interno de resultado de la operación
            
+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 121 (number) - identificador del error
            + result_message: BILL_NOT_FOUND (string) - código interno de resultado de la operación

+ Response 501 (application/json)

     + Attributes(object)
            + result_code: 13 (number) - identificador del error
            + result_message: NOT_IMPLEMENTED (string) - código interno de resultado de la operación

### Notificar devolución de venta a Shopping origen MachineUse [POST /sale/v1/refund/{buildingId}/bill/{billId}]

- Clientes: BackOffice

- Propósito: Notificación de devolución de venta manual con origen Machine Use.

+ Parameters
    + buildingId: 32 (number) - Id de edificio.
    + billId: 35831 (number) - Id del uso.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 400 (application/json)
     + Attributes(object)
            + result_code: 169 (number) - identificador del error
            + result_message: CARD_IS_MASTER (string) - código interno de resultado de la operación

+ Attributes(object)
            + result_code: 167 (number) - identificador del error
            + result_message: SALE_ALREADY_REFUNDED (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 166 (number) - identificador del error
            + result_message: SOAP_SERVICE_ERROR (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 170 (number) - identificador del error
            + result_message: SALE_NOT_NOTIFIED (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 68 (number) - identificador del error
            + result_message: MACHINE_USE_NOT_FOUND (string) - código interno de resultado de la operación

+ Response 501 (application/json)

     + Attributes(object)
            + result_code: 13 (number) - identificador del error
            + result_message: NOT_IMPLEMENTED (string) - código interno de resultado de la operación
