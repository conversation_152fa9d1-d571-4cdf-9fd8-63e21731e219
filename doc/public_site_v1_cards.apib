FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - Integracion Public Site <> API

## Cards [/public-site/v1/cards]

### Available payment methods [GET /public-site/v1/cards/{card_uid}/methods]

- Clientes: Public Site

- Propósito: Obtener los metodos de pago con los cueles es posible operar para una tarjeta.

+ Parameters
    + card_uid: 0xaa0a0aaa (string, required) - identificador de una tarjeta (card.uuid)


+ Response 200 (application/json)

    + Attributes(object)
        + payment_methods: (array)
            + payment_method: (object)
                + name: <PERSON><PERSON><PERSON> (string) - nombre del medio de pago
        + splitting: true (boolean) - si se va a aplicar una división de fondos cuando se realice el pago
        + price_customer: 100 (number) - precio del uso para los clientes
        + machine_rate: 120 (number) - precio del uso para los clientes, si hay máquinas de diferentes capacidades en el edificio
        + special_rate_message: Máquina de 14kg (string) - mensaje asociado al precio, si hay máquinas de diferentes capacidades en el edificio


+ Response 409 (application/json)

    Cuando se intenta cargar saldo a una tarjeta POSTPAID. No está permitido.

    + Attributes(object)
        + result_code: 70 (number) - identificador del error
        + result_message: CARD_NOT_PREPAID (string) - código interno de resultado de la operación
        + result_detail: Su tarjeta es POSTPAGO, no requiere precarga... (string) - mensaje más detallado


+ Response 400 (application/json)

    Cuando la tarjeta esta marcada como perdida

    + Attributes(object)
        + result_code: 166 (number) - identificador del error
        + result_message: CARD_LOST (string) - código interno de resultado de la operación

+ Response 400 (application/json)

    Cuando la tarjeta esta bloqueada debido a una solicitud de la administracion

    + Attributes(object)
        + result_code: 167 (number) - identificador del error
        + result_message: CARD_SUSPENDED (string) - código interno de resultado de la operación


+ Response 400 (application/json)

    Cuando la tarjeta esta deshabilitada y se desconoce la razon.

    + Attributes(object)
        + result_code: 100 (number) - identificador del error
        + result_message: INACTIVE_CARD (string) - código interno de resultado de la operación


+ Response 400 (application/json)

    Cuando la tarjeta fisica (no virtual) no esta asociada a un edificio.

    + Attributes(object)
        + result_code: 159 (number) - identificador del error
        + result_message: CARD_NOT_FROM_BUILDING (string) - código interno de resultado de la operación
