FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - API para manejo de dispensadores de jabón y suavizante


### Create Soap Dispenser [PUT    /api/v1/dispensers/:dispenserId]

+ Clientes: BackOffice

+ Propósito: actualizar un dispensador de jabón y suavizante

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
        + model: modelo nuevo (string) - modelo del dispensador
        + description: dispensador bueno (string) - descripción del dispensador
        + machineSerial: 119K2DD2E333 (string, required) - número de serie de la máquina del dispensador


+ Response 201 (application/json)

+ Response 404 (application/json)

    + Attributes(object)
            + result_code: 112 (number) - identificador del error
            + result_message: SOAP_DISPENSER_NOT_FOUND (string) - código interno de resultado de la operación

+ Response 404 (application/json)

    + Attributes(object)
            + result_code: 9 (number) - identificador del error
            + result_message: ex_machine_not_found (string) - código interno de resultado de la operación


### Create Soap Dispenser [PUT    /api/v1/dispensers/:dispenserId/replenishUses]

+ Clientes: BackOffice

+ Propósito: actualiza los usos que tiene el dispensador de jabón y suavizante de acuerdo a la fecha de recarga del bidón

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
        + rechargeDate: `2023-08-27 16:20:00.0` (string) - fecha de recarga
        + isRecharge: true (string) - indica si se recargo el bidón del dispensador. Valores esperados: true, false


+ Response 201 (application/json)

+ Response 404 (application/json)

    + Attributes(object)
            + result_code: 112 (number) - identificador del error
            + result_message: SOAP_DISPENSER_NOT_FOUND (string) - código interno de resultado de la operación
