FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - Integracion Public Site <> API

## Edificios [/public-site/v1/administrations/buildings]
### Colivings [GET /public-site/v1/administrations/buildings/coliving{?level}]

- Clientes: Public Site

- Propósito: Obtener los edificios coliving que una administracion gestiona.

+ Parameters
    + level: 0 (number) - indica el nivel de datos a ser devuelto, cuanto mayor mas informacion. Comienza en 0 (cero)

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + buildings: (array) - lista de edificios
            + building: (object)
                + id: 1 (number) - identificador interno de un edificio
                + name: Edificio Uno (string) - nombre de un edificio

### Nuevo Usuario [POST /public-site/v1/administrations/buildings/{buildingId}/users]

- Clientes: Public Site

- Propósito: Crear nuevo usuario para el Coliving.

+ Parameters
    + buildingId: 1 (number) - indica el identificador del edificio.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)
        + firstname: Juan (string, required) - Nombre del nuevo usuario.
        + lastname: Perez (string, required) - Apellido del nuevo usuario.
        + email: <EMAIL> (string, required) - Email del nuevo usuario (único).
        + reference: Unidad 101 (string, required) - Referencia a la Unit.

+ Response 200 (application/json)

    + Attributes(object)
            + user: (object)
                + id: 1 (number) - identificador interno del nuevo usuario
                + firstname: Juan (string) - nombre del nuevo usuario
                + lastname: Perez (string) - apellido del nuevo usuario
                + email: <EMAIL> (string) - email del nuevo usuario
                + reference: Unidad 101 (string) - referencia a la Unit

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 2 (number) - identificador del error
        + result_message: BUILDING_NOT_FOUND

+ Response 409 (application/json)

    + Attributes(object)
        + result_code: 7 (number) - identificador del error
        + result_message: USER_ALREADY_EXISTS

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 147 (number) - identificador del error
        + result_message: BUILDING_MAX_UNITS_REACHED

### Listar Usuarios [GET /public-site/v1/administrations/buildings/{buildingId}/users]

- Clientes: Public Site

- Propósito: Listar usuarios del Coliving.

+ Parameters
    + buildingId: 1 (number) - indica el identificador del edificio.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + users: (array) - lista de usuarios
            + user: (object)
                + id: 1 (number) - identificador interno del nuevo usuario
                + firstname: Juan (string) - nombre del nuevo usuario
                + lastname: Perez (string) - apellido del nuevo usuario
                + email: <EMAIL> (string) - email del nuevo usuario
                + reference: Unidad 101 (string) - referencia a la Unit
                + createdAt: `2022-07-24 18:15:34.0` (string) - creación de la Account
                + validatedAt: `2022-08-24 18:15:34.0` (string) - fecha de validación de cuenta

## Eliminar Usuario [DELETE /public-site/v1/administrations/buildings/{buildingId}/users/{userId}]

- Clientes: Public Site

- Propósito: Eliminar un usuario del Coliving.

+ Parameters
    + buildingId: 1 (number) - indica el identificador del edificio.
    + userId: 1 (number) - indica el identificador del usuario.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

### Confirmar Usuario [POST /public-site/v1/users/confirm]

- Clientes: Public Site

- Propósito: Confirmar cuenta de usuario para el Coliving.

+ Request

    + Attributes(object)
        + token: 5da06522-c6d2-4e68-9591-0780b8db16c2 (string, required) - token generado para validar usuario.
        + password: tH3P4szW0rD88 (string, required) - Nuevo passowrd del usuario.

+ Response 200 (application/json)

    + Attributes(object)
            + user: (object)
                + id: 1 (number) - identificador interno del nuevo usuario
                + firstname: Juan (string) - nombre del nuevo usuario
                + lastname: Perez (string) - apellido del nuevo usuario
                + email: <EMAIL> (string) - email del nuevo usuario
                + reference: Unidad 101 (string) - referencia a la Unit

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 149 (number) - identificador del error
        + result_message: INVALID_TOKEN

+ Response 400 (application/json)

    + Attributes(object)
        + result_code: 4 (number) - identificador del error
        + result_message: MISSING_PARAMETERS

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 3 (number) - identificador del error
        + result_message: USER_NOT_FOUND

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 148 (number) - identificador del error
        + result_message: TOKEN_NOT_FOUND

### Validar Token de confirmación [POST /public-site/v1/users/verify-token]

- Clientes: Public Site

- Propósito: Verificar validez del token de confirmación de Usuario.

+ Request

    + Attributes(object)
        + token: 5da06522-c6d2-4e68-9591-0780b8db16c2 (string, required) - token generado para validar usuario.

+ Response 200 (application/json)

    + valid (boolean) - validez del token provisto

### Reenviar Email de Confirmación de Usuario [POST /public-site/v1/users/{userId}/resend-confirmation]

- Clientes: Public Site

- Propósito: Reenviar Email de Confirmación de Usuario con un nuevo Token.

+ Parameters
    + userId: 1 (number) - indica el identificador del usuario.

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

### Reporte de usos por tarjeta [GET /public-site/v1/reports/sendReportByUid{?user}{&card}]

- Clientes: Public site

- Propósito: Enviarle a los usurios un reporte de sus usos vía mail

+ Parameters
    + user: <EMAIL> (string, required) - email del usuario a donde se enviara el reporte
    + card: 0xaa0a0aaa (string, required) - identificador de una tarjeta (card uuid)

+ Response 200 (text/html)

+ Response 404 (application/json)

    + Attributes(object)
        + result_code: 100 (number) - identificador del error
        + result_message: INACTIVE_CARD
        
