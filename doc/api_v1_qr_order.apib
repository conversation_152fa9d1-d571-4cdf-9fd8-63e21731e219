FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - API BackOffice para cargar saldo manual

## QR Order [GET /api/v1/qr/order]

+ Clientes: BackOffice

+ Propósito: Activar una máquina por QR

+ Parameters
    + serial: 427ABCD11742 (string, required) - Número serial de una máquina
    + rut: 101010101101 (string) - RUT identificador de la empresa
    + customerName: POLLOS HERMANOS SA (string) - razón social de la empresa



+ Response 200 (application/json)

    + Attributes(object)
            + Transaction_token: `x1x11111-1x11-11x1-xxx1-1111x1111112` (string) - token de autorización
            + Transaction_currency: UYU (string) - divisa monetaria
            + Transaction_amount: 100 (number) - monto de transacción
            + Transaction_id: 12345 (number) - número identificador


+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 153 (number) - identificador del error
            + result_message: MACHINE_BUSY (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 4 (number) - identificador del error
            + result_message: ex_missing_parameters (string) - código interno de resultado de la operación

+ Response 400 (application/json)

     + Attributes(object)
            + result_code: 117 (number) - identificador del error
            + result_message: MACHINE_DISABLED (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 9 (number) - identificador del error
            + result_message: ex_machine_not_found (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 2 (number) - identificador del error
            + result_message: ex_building_not_found (string) - código interno de resultado de la operación

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 71 (number) - identificador del error
            + result_message: ex_rate_not_found (string) - código interno de resultado de la operación
