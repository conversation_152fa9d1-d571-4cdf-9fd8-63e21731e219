FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy/

# LAVOMAT - API para Rates

## Rate [/api/v1/rates]

### Create rates [POST    /api/v1/rates/{rateId}/rateEvents]

+ Clientes: BackOffice

+ Propósito: Para crear un rate event

+ Parameters
    + rateId: 1 (number, required) - identificador del rate

+ Request

    + Headers

        X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

    + Attributes(object)

        + minUses: 0 (number) - cantidad de usos mínimos de edificio
        + minUsesPerUnit: 2 (number) - cantidad de usos mínimos por unidad
        + customerPrice: 89 (number, required) - precio del cliente
        + companyPrice: 104 (number, required) - precio de la empresa
        + m3Price: 0 (number) - precio por kwh
        + cardReplacementPrice: 350 (number, required) - costo del delivery de la tarjeta lavomat
        + validFrom: `2023-07-10 09:00:00.0` (string, required) - fecha de comienzo de validez del rate event
        + validUntil: `2043-07-10 09:00:00.0` (string, required) - fecha de fin de validez del rate event


+ Response 200 (application/json)

+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 7 (number) - identificador del error
            + result_message: ex_rate_event_not_found (string) - código interno de resultado de la operación


### Create actualizar [PUT    /api/v1/rates/{rateId}/rateEvents/{id}]

+ Clientes: BackOffice

+ Propósito: Para modificar un rate event

+ Parameters
    + rateId: 1 (number, required) - identificador del rate
    + id: 33 (number, required) - identificador del rate

    + Request

        + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

        + Attributes(object)

            + minUses: 72 (number) - cantidad de usos mínimos de edificio
            + minUsesPerUnit: 0 (number) - cantidad de usos mínimos por unidad
            + customerPrice: 89 (number, required) - precio del cliente
            + companyPrice: 104 (number, required) - precio de la empresa
            + m3Price: 0 (number) - precio por kwh
            + cardReplacementPrice: 350 (number, required) - costo del delivery de la tarjeta lavomat
            + validFrom: `2023-07-10 09:00:00.0` (string, required) - fecha de comienzo de validez del rate event
            + validUntil: `2043-07-10 09:00:00.0` (string, required) - fecha de fin de validez del rate event


+ Response 404 (application/json)

     + Attributes(object)
            + result_code: 7 (number) - identificador del error
            + result_message: ex_rate_event_not_found (string) - código interno de resultado de la operación
