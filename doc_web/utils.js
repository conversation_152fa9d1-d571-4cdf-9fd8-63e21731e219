const fs = require('fs'),
  path = require('path'),
  aglio = require('aglio')

const CONTAINER_FOLDER = 'dist'
const DOCUMENT_EXTENSION = '.apib'

const getDocs = () =>
  fs
    .readdirSync(CONTAINER_FOLDER)
    .filter((item) => path.extname(item) == DOCUMENT_EXTENSION)
    .map((item) => {
      const name = path.parse(item).name

      return {
        name: name.replaceAll('_', '/'),
        url: `/${name}`,
        absolutePath: path.join(
          __dirname,
          CONTAINER_FOLDER,
          `${name}${DOCUMENT_EXTENSION}`
        ),
      }
    })

const DOCS = getDocs()

const getByUrl = (url) => DOCS.find((item) => item.url === url)

const readFile = (...args) => {
  return new Promise((resolve, reject) => {
    fs.readFile(...args, (err, data) => {
      if (err) return reject(err)
      resolve(data)
    })
  })
}

const aglioRender = (...args) => {
  return new Promise((resolve, reject) => {
    aglio.render(...args, (err, html, warnings) => {
      if (err) {
        console.log(err)
        return reject(err)
      }
      if (warnings) console.log(warnings)

      resolve(html)
    })
  })
}

const getHtmlByDoc = async ({ name, absolutePath }) => {
  const blueprint = await readFile(absolutePath, 'utf8')

  const html = await aglioRender(blueprint, { themeVariables: 'default' })

  return html
}

module.exports = {
  DOCS,
  CONTAINER_FOLDER,
  getByUrl,
  getHtmlByDoc,
}
