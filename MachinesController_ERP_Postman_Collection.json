{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "ERP Machines Controller API", "description": "Postman collection for domains.erp.controllers.v1.MachinesController\n\nThis collection includes endpoints for managing machines in the ERP domain, specifically for Odoo integration.\n\n**Authentication Required:**\n- All endpoints require authentication via X-LM-AUTH-TOKEN header\n- Use the {{authToken}} environment variable\n\n**Base URL:**\n- {{baseUrl}}/erp/odoo/v1\n\n**Endpoints:**\n1. Create Machine - POST /machines\n2. Update Machine - PUT /machines", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Machine", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201 Created\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "{{authToken}}", "type": "text", "description": "Authentication token required for all API calls"}], "body": {"mode": "raw", "raw": "{\n  \"odooId\": 12345,\n  \"name\": \"Washing Machine A1\",\n  \"model\": \"WM-2000\",\n  \"description\": \"High-efficiency washing machine for commercial use\",\n  \"serialNumber\": \"WM2000-001-2024\",\n  \"unitPrice\": 1500.00,\n  \"uyPrice\": 45000.00,\n  \"averageUseTime\": 45,\n  \"machineType\": \"WASHER\",\n  \"reference\": \"REF-WM-001\",\n  \"capacity\": 8,\n  \"expectedUses\": 1000,\n  \"state\": \"NEW\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/erp/odoo/v1/machines", "host": ["{{baseUrl}}"], "path": ["erp", "odoo", "v1", "machines"]}, "description": "Creates a new machine in the system.\n\n**Required Fields:**\n- `odooId` (integer): Unique Odoo identifier (must be > 0)\n- `name` (string): Machine name\n- `serialNumber` (string): Unique serial number\n- `state` (string): Machine state (NEW, ACTIVE, INACTIVE, etc.)\n- `machineType` (string): Type of machine (WASHER, DRYER, etc.)\n\n**Optional Fields:**\n- `model` (string): Machine model\n- `description` (string): Machine description\n- `unitPrice` (number): Price in USD\n- `uyPrice` (number): Price in UYU\n- `averageUseTime` (integer): Average use time in minutes\n- `reference` (string): Reference code\n- `capacity` (integer): Machine capacity\n- `expectedUses` (integer): Expected number of uses\n\n**Validation:**\n- Serial number must be unique\n- Odoo ID must be unique\n- Machine model will be created if it doesn't exist"}, "response": [{"name": "Success - Machine Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "{{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"odooId\": 12345,\n  \"name\": \"Washing Machine A1\",\n  \"model\": \"WM-2000\",\n  \"description\": \"High-efficiency washing machine\",\n  \"serialNumber\": \"WM2000-001-2024\",\n  \"unitPrice\": 1500.00,\n  \"uyPrice\": 45000.00,\n  \"averageUseTime\": 45,\n  \"machineType\": \"WASHER\",\n  \"reference\": \"REF-WM-001\",\n  \"capacity\": 8,\n  \"expectedUses\": 1000,\n  \"state\": \"NEW\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/erp/odoo/v1/machines", "host": ["{{baseUrl}}"], "path": ["erp", "odoo", "v1", "machines"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": ""}]}, {"name": "Update Machine", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 OK\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "{{authToken}}", "type": "text", "description": "Authentication token required for all API calls"}], "body": {"mode": "raw", "raw": "{\n  \"odooId\": 12345,\n  \"name\": \"Updated Washing Machine A1\",\n  \"model\": \"WM-2000-Pro\",\n  \"description\": \"Updated high-efficiency washing machine for commercial use\",\n  \"serialNumber\": \"WM2000-001-2024\",\n  \"unitPrice\": 1600.00,\n  \"uyPrice\": 48000.00,\n  \"averageUseTime\": 50,\n  \"machineType\": \"WASHER\",\n  \"reference\": \"REF-WM-001-UPD\",\n  \"capacity\": 10,\n  \"expectedUses\": 1200,\n  \"state\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/erp/odoo/v1/machines", "host": ["{{baseUrl}}"], "path": ["erp", "odoo", "v1", "machines"]}, "description": "Updates an existing machine in the system. The machine is identified by the `odooId` field in the request body.\n\n**Required Fields:**\n- `odooId` (integer): Odoo identifier of the machine to update (must exist)\n- `name` (string): Updated machine name\n- `serialNumber` (string): Updated serial number\n- `state` (string): Updated machine state\n- `machineType` (string): Updated machine type\n\n**Optional Fields:**\n- `model` (string): Updated machine model\n- `description` (string): Updated description\n- `unitPrice` (number): Updated price in USD\n- `uyPrice` (number): Updated price in UYU\n- `averageUseTime` (integer): Updated average use time\n- `reference` (string): Updated reference code\n- `capacity` (integer): Updated capacity\n- `expectedUses` (integer): Updated expected uses\n\n**Validation:**\n- Machine with the specified odooId must exist\n- Serial number must exist in the system\n- Machine model will be created if it doesn't exist"}, "response": [{"name": "Success - Machine Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "X-LM-AUTH-TOKEN", "value": "{{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"odooId\": 12345,\n  \"name\": \"Updated Washing Machine A1\",\n  \"model\": \"WM-2000-Pro\",\n  \"description\": \"Updated description\",\n  \"serialNumber\": \"WM2000-001-2024\",\n  \"unitPrice\": 1600.00,\n  \"uyPrice\": 48000.00,\n  \"averageUseTime\": 50,\n  \"machineType\": \"WASHER\",\n  \"reference\": \"REF-WM-001-UPD\",\n  \"capacity\": 10,\n  \"expectedUses\": 1200,\n  \"state\": \"ACTIVE\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/erp/odoo/v1/machines", "host": ["{{baseUrl}}"], "path": ["erp", "odoo", "v1", "machines"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": ""}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for the entire collection", "// You can add common setup logic here"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test script for the entire collection", "pm.test(\"Content-Type is application/json or empty\", function () {", "    const contentType = pm.response.headers.get(\"Content-Type\");", "    if (contentType) {", "        pm.expect(contentType).to.include(\"application/json\");", "    }", "});"]}}], "variable": [{"key": "baseUrl", "value": "https://app-sandbox-ec2.lavomat.com.uy", "description": "Base URL for the API. Change this for different environments (sandbox, production, etc.)"}, {"key": "authToken", "value": "", "description": "Authentication token obtained from /api/v1/signin endpoint. Set this after successful authentication."}]}