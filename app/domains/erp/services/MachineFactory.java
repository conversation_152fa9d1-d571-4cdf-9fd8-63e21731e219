package domains.erp.services;

import domains.erp.dto.machines.MachineCreateParameters;
import domains.erp.dto.machines.MachineUpdateParameters;
import global.APIException;
import services.BaseService;

public class MachineFactory extends BaseService {

    public void create(MachineCreateParameters dto) throws APIException {
        try {
            dto.getMachine().save();
        } catch (Exception e) {
            loggerError("Error while creating machine - error: {}", e.getMessage(), e);
            throw APIException.raise(APIException.APIErrors.INTERNAL_SERVER_ERROR);
        }
    }

    public void update(MachineUpdateParameters dto) throws APIException {
        try {
            dto.getMachine().update();
        } catch (Exception e) {
            loggerError("Error while updating machine - error: {}", e.getMessage(), e);
            throw APIException.raise(APIException.APIErrors.INTERNAL_SERVER_ERROR);
        }
    }
}
