package domains.erp.dto.machines;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.MissingParametersException;
import global.exceptions.machines.MachineNotFoundException;
import models.Machine;
import models.Part.PartState;
import queries.machines.MachineQuery;

import java.util.Date;

public class MachineRetireParameters extends dto.JsonBodyActionParameters {

    protected static final String STATE = "state";
    protected static final String RETIRED_DATE = "retirendDate";

    protected final PartState state;
    protected final Date retiredDate;

    Machine machine;

    public MachineRetireParameters(int odooId, JsonNode body) throws APIException {
        this.machine = new MachineQuery().filterByOdooId(odooId).single();
        this.state = safeEnum(PartState.class, STATE, body, null);
        this.retiredDate = safeDate(RETIRED_DATE, body, null);
    }

    public MachineRetireParameters validate() throws APIException {
        if (this.machine == null) {
            throw new MachineNotFoundException();
        }

        if (this.state == null) {
            throw new MissingParametersException(STATE);
        }

        if (this.retiredDate == null) {
            throw new MissingParametersException(RETIRED_DATE);
        }

        this.machine.setState(this.state);
        this.machine.setRetiredDate(this.retiredDate);

        return this;
    }

    public Machine getMachine() {
        return this.machine;
    }
}
