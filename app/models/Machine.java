package models;

import com.play4jpa.jpa.models.DefaultQuery;
import org.hibernate.criterion.Restrictions;
import play.mvc.Http.Request;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
public class Machine extends Part {

    public enum MachineType {
        WASHER,
        DRYER,
    }

    // Nuevo: tipo de edificio
    public enum StatusActivation {
        READY,
        IN_PROGRESS,
        ACTIVATED,
        OTHER,
    }

    public static int DISABLED_SORT_INDEX = 0;

    /**
     *
     */

    private static final long serialVersionUID = 5905191069477004035L;

    private int odooId;

    @ManyToOne(cascade = CascadeType.ALL)
    private Building building;

    @Enumerated(value = EnumType.STRING)
    private MachineType machineType;

    private String englishDescription;
    private double unitPrice;
    private double uyPrice;

    private int expectedUses;
    private int currentUses;

    private Date lastAlive;

    @ManyToOne
    private Firmware firmware;

    /**
     * Explicit version of the firmware sent by R<PERSON>.
     * The purpose of this field is to store the current version of the
     * firmware.
     * The main difference with {@link #firmware} is it is only updated through
     * the upgrade process, when {@link #upgradeTo} is modified.
     * More info: https://app.shortcut.com/lavomat/story/2435
     */
    private String firmwareVersion;

    private String upgradeTo;

    // max length: 255
    private String publicIp;
    // max length: 255
    private String privateIp;
    // max length: 255
    private String port;

    private int averageUseTime;
    private static final int DEFAULT_AVERAGE_TIME = 40;
    private static final int DEFAULT_MAINTENANCE_TIME = 100;

    private int pendingUses;

    @Column(name = "sort_index")
    private int sortIndex;

    private boolean isTopicEnable = true;

    private int capacity;

    private String reference;

    // Se agrega un rate a nivel de machine para aqullas que cuentan con
    // dosificadoras de jabon
    // Si el rate asociado a la maquina es null se debe considerar el rate de
    // building al que esta asociada

    @OneToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REFRESH})
    private Rate machineRate;

    @Enumerated(value = EnumType.STRING)
    private StatusActivation activation_status;

    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE})
    public MachineModel machineModel;

    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE})
    private Machine rpiChild;

    /**
     * QR URL.
     * This url stores the QR's URL that can be found pasted on the physical
     * machine.
     */
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "machine")
    private TinyUrl qr;

    private Date retiredDate;

    public Machine() {
        super();
        this.capacity = 15;
        this.sortIndex = 0;
    }

    public Machine(
        String name,
        String model,
        String description,
        PartState state,
        String serialNumber
    ) {
        super(name, model, description, state, serialNumber);
        this.capacity = 15;
        this.sortIndex = 0;
    }

    @SuppressWarnings("unchecked")
    public static List<Machine> findAll() {
        DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();

        query.getCriteria().add(Restrictions.eq("class", Machine.class.getSimpleName()));

        return query.findList();
    }

    public static long count() {
        @SuppressWarnings("unchecked")
        DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();

        query.getCriteria().add(Restrictions.eq("class", Machine.class.getSimpleName()));

        return query.findRowCount();
    }

    @SuppressWarnings("unchecked")
    public static Machine findById(int machineId) {
        DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();

        return query.byId(machineId);
    }

    @SuppressWarnings("unchecked")
    public static Machine findBySerialNumber(String serial) {
        DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();

        return query.eq("serialNumber", serial).findUnique();
    }

    @SuppressWarnings("unchecked")
    public static List<Machine> findWithFilters(Request filters) {
        /*
         * DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();
         *
         * query.getCriteria().add(Restrictions.eq("class",
         * Machine.class.getSimpleName()));
         *
         * return query.findList();
         */

        DefaultQuery<Machine> query = (DefaultQuery<Machine>) finder.query();

        String keyword = filters.getQueryString("machineSearch");

        query.getCriteria().add(Restrictions.eq("class", Machine.class.getSimpleName()));

        if (keyword != null) {
            query.ilike("serialNumber", "%" + keyword + "%");
        }

        List<Machine> result = query.findList();

        return result;
    }

    public int getOdooId() {
        return this.odooId;
    }

    public void setOdooId(int odooId) {
        this.odooId = odooId;
    }

    public Building getBuilding() {
        return this.building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public boolean hasSpecialRate() {
        return (
            this.building != null &&
                this.machineRate != null &&
                this.building.getRate() != this.machineRate
        );
    }

    public MachineType getMachineType() {
        return machineType;
    }

    public void setMachineType(MachineType machineType) {
        this.machineType = machineType;
    }

    public String getEnglishDescription() {
        return englishDescription;
    }

    public void setEnglishDescription(String englishDescription) {
        this.englishDescription = englishDescription;
    }

    public double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public double getUyPrice() {
        return uyPrice;
    }

    public void setUyPrice(double uyPrice) {
        this.uyPrice = uyPrice;
    }

    public int getExpectedUses() {
        return expectedUses;
    }

    public void setExpectedUses(int expectedUses) {
        this.expectedUses = expectedUses;
    }

    public int getCurrentUses() {
        return currentUses;
    }

    public void setCurrentUses(int currentUses) {
        this.currentUses = currentUses;
    }

    public void incrementUsesCount() {
        this.currentUses = this.currentUses + 1;
    }

    public Firmware getFirmware() {
        return firmware;
    }

    public void setFirmware(Firmware firmware) {
        this.firmware = firmware;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        this.firmwareVersion = firmwareVersion;
    }

    public String getFirmwareVersion() {
        return this.firmwareVersion;
    }

    public String getUpgradeTo() {
        return upgradeTo;
    }

    public void setUpgradeTo(String upgradeTo) {
        this.upgradeTo = upgradeTo;
    }

    public String getPublicIp() {
        return publicIp;
    }

    public void setPublicIp(String publicIp) {
        if (publicIp.length() > 255) {
            play.Logger.error("Data too long for Machine.publicIp: {}", publicIp);
            return;
        }

        this.publicIp = publicIp;
    }

    public String getPrivateIp() {
        return privateIp;
    }

    public void setPrivateIp(String privateIp) {
        if (privateIp.length() > 255) {
            play.Logger.error("Data too long for Machine.privateIp: {}", privateIp);
            return;
        }

        this.privateIp = privateIp;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        if (port.length() > 255) {
            play.Logger.error("Data too long for Machine.port: {}", port);
            return;
        }

        this.port = port;
    }

    public Rate getMachineRate() {
        if (!this.hasSpecialRate() && this.building != null) {
            return this.building.getRate();
        } else {
            return this.machineRate;
        }
    }

    public Rate getSpecialRate() {
        return this.machineRate;
    }

    public void setMachineRate(Rate rate) {
        this.machineRate = rate;
    }

    public StatusActivation getStatusActivation() {
        return this.activation_status;
    }

    public void setStatusActivation(StatusActivation status) {
        this.activation_status = status;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!Machine.class.isAssignableFrom(obj.getClass())) return false;
        return ((Machine) obj).getId() == this.getId();
    }

    /**
     * @return the averageUseTime
     */
    public int getAverageUseTime() {
        return this.averageUseTime != 0 ? this.averageUseTime : this.DEFAULT_AVERAGE_TIME;
    }

    /**
     * @return the averageUseTime
     */
    public int getMaintenanceTime() {
        return this.DEFAULT_MAINTENANCE_TIME;
    }

    /**
     * @param averageUseTime the averageUseTime to set
     */
    public void setAverageUseTime(int averageUseTime) {
        this.averageUseTime = averageUseTime;
    }

    public int getPendingUses() {
        return pendingUses;
    }

    public void setPendingUses(int pendingUses) {
        this.pendingUses = pendingUses;
    }

    public int getSortIndex() {
        return this.sortIndex;
    }

    public void setSortIndex(int index) {
        this.sortIndex = index;
    }

    // Flag to control MQTT topic
    public boolean getIsTopicEnable() {
        return isTopicEnable;
    }

    public void setIsTopicEnable(boolean enable) {
        this.isTopicEnable = enable;
    }

    public int getCapacity() {
        return this.capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public String getReference() {
        return this.reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public MachineModel getMachineModel() {
        return this.machineModel;
    }

    public void setMachineModel(MachineModel machineModel) {
        this.machineModel = machineModel;
    }

    public Machine getRPIChild() {
        return this.rpiChild;
    }

    public void setRpiChild(Machine child) {
        this.rpiChild = child;
    }

    /*
     * ==== Keep Alive
     */

    public Date getLastAlive() {
        return lastAlive;
    }

    public void setLastAlive(Date lastAlive) {
        this.lastAlive = lastAlive;
    }

    public boolean isKeepAliveAfterDate(Date date) {
        return date != null && this.lastAlive != null && this.lastAlive.getTime() >= date.getTime();
    }

    /**
     * Determines whether a machine can be used.
     */
    public boolean isOperational() {
        return PartState.NEW == this.getState() || PartState.ACTIVE == this.getState();
    }

    public TinyUrl getQr() {
        return this.qr;
    }

    public Date getRetiredDate() {
        return this.retiredDate;
    }

    public void setRetiredDate(Date retiredDate) {
        this.retiredDate = retiredDate;
    }
}
